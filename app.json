{"expo": {"name": "2nd Car", "slug": "second-car", "version": "1.2.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "com.factcoding.secondcar", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.factcoding.secondcar", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}, "CFBundleURLTypes": [{"CFBundleURLName": "facebook", "CFBundleURLSchemes": ["fb1215086369859674"]}]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.factcoding.secondcar", "versionCode": 15, "enableProguardInReleaseBuilds": true, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE"], "intentFilters": [{"action": "VIEW", "category": ["DEFAULT", "BROWSABLE"], "data": {"scheme": "fb1215086369859674"}}]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "config": {"firebase": {"apiKey": "${process.env.EXPO_PUBLIC_FIREBASE_API_KEY}", "authDomain": "${process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN}", "databaseURL": "${process.env.EXPO_PUBLIC_FIREBASE_DATABASE_URL}", "projectId": "${process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID}", "storageBucket": "${process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET}", "messagingSenderId": "${process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}", "appId": "${process.env.EXPO_PUBLIC_FIREBASE_APP_ID}", "measurementId": "${process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID}"}}}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-build-properties", {"android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0"}, "ios": {"useFrameworks": "static"}}], "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "b20f2400-ee31-4066-bb39-06fada796d21"}}, "owner": "2ndcar2025"}}