{"name": "second-car", "main": "expo-router/entry", "version": "1.2.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/metro-config": "~0.19.0", "@expo/vector-icons": "^14.0.4", "@miblanchard/react-native-slider": "^2.6.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.5.1", "axios": "^1.7.9", "expo": "^52.0.40", "expo-auth-session": "~6.0.3", "expo-blur": "~14.0.3", "expo-build-properties": "^0.13.2", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.8", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.12", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-router": "~4.0.17", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-web-browser": "~14.0.2", "firebase": "^11.4.0", "formik": "^2.4.6", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.20.2", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-view-shot": "~4.0.3", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~52.0.4", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}